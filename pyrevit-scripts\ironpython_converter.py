#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Conversor IronPython para RFA → OBJ
Funciona com IronPython 2 do pyRevit
"""

import sys
import os

# Verificar se estamos no contexto do pyRevit
try:
    import clr
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    
    from Autodesk.Revit.DB import *
    from pyrevit import revit
    
    PYREVIT_CONTEXT = True
    print("=== BIMEX IronPython Converter ===")
    print("Contexto pyRevit detectado")
    
except ImportError as e:
    PYREVIT_CONTEXT = False
    print("ERRO: Contexto pyRevit nao disponivel")
    print("Este script deve ser executado com: pyrevit run")
    sys.exit(1)

def convert_rfa_to_obj():
    """Converte RFA para OBJ usando IronPython"""
    
    # Obter argumentos
    args = sys.argv
    if len(args) < 2:
        print("ERRO: Arquivo RFA nao especificado")
        return False
    
    input_file = args[1]
    output_file = os.path.splitext(input_file)[0] + ".obj"
    
    print("Arquivo RFA: " + input_file)
    print("Arquivo OBJ: " + output_file)
    
    # Verificar se arquivo existe
    if not os.path.exists(input_file):
        print("ERRO: Arquivo nao encontrado")
        return False
    
    try:
        # Obter aplicação do Revit
        app = revit.app
        if app is None:
            print("ERRO: Aplicacao Revit nao disponivel")
            return False
        
        print("Aplicacao Revit: " + app.VersionName)
        
        # Abrir documento
        print("Abrindo documento...")
        doc = app.OpenDocumentFile(input_file)
        
        if doc is None:
            print("ERRO: Falha ao abrir documento")
            return False
        
        print("Documento aberto: " + doc.Title)
        
        # Verificar se é família
        if not doc.IsFamilyDocument:
            print("ERRO: Nao e familia")
            doc.Close(False)
            return False
        
        # Coletar elementos
        collector = FilteredElementCollector(doc)
        elements = list(collector.WhereElementIsNotElementType().ToElements())
        print("Elementos: " + str(len(elements)))
        
        # Processar geometria
        vertices = []
        faces = []
        vertex_index = 1
        
        for element in elements:
            try:
                geom = element.get_Geometry(Options())
                if geom is None:
                    continue
                
                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        print("Processando solido...")
                        
                        for face in geom_obj.Faces:
                            try:
                                mesh = face.Triangulate()
                                if mesh is None:
                                    continue
                                
                                for i in range(mesh.NumTriangles):
                                    triangle = mesh.get_Triangle(i)
                                    
                                    v_indices = []
                                    for j in range(3):
                                        vertex = triangle.get_Vertex(j)
                                        x = vertex.X * 0.3048
                                        y = vertex.Y * 0.3048
                                        z = vertex.Z * 0.3048
                                        
                                        vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                        v_indices.append(vertex_index)
                                        vertex_index += 1
                                    
                                    faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                            except:
                                continue
                                
                    elif isinstance(geom_obj, GeometryInstance):
                        print("Processando instancia...")
                        try:
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    for face in inst_obj.Faces:
                                        try:
                                            mesh = face.Triangulate()
                                            if mesh is None:
                                                continue
                                            
                                            for i in range(mesh.NumTriangles):
                                                triangle = mesh.get_Triangle(i)
                                                
                                                v_indices = []
                                                for j in range(3):
                                                    vertex = triangle.get_Vertex(j)
                                                    x = vertex.X * 0.3048
                                                    y = vertex.Y * 0.3048
                                                    z = vertex.Z * 0.3048
                                                    
                                                    vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                                    v_indices.append(vertex_index)
                                                    vertex_index += 1
                                                
                                                faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                        except:
                                            continue
                        except:
                            continue
            except:
                continue
        
        print("Vertices: " + str(len(vertices)))
        print("Faces: " + str(len(faces)))
        
        # Criar arquivo OBJ
        try:
            with open(output_file, 'w') as f:
                f.write("# BIMEX IronPython Converter\n")
                f.write("# Familia: " + doc.Title + "\n")
                f.write("# Vertices: " + str(len(vertices)) + "\n")
                f.write("# Faces: " + str(len(faces)) + "\n")
                f.write("# Convertido com pyRevit IronPython\n\n")
                
                for vertex in vertices:
                    f.write(vertex + "\n")
                
                f.write("\n")
                
                for face in faces:
                    f.write(face + "\n")
            
            print("Arquivo criado: " + output_file)
            
            # Verificar tamanho
            file_size = os.path.getsize(output_file)
            print("Tamanho: " + str(file_size) + " bytes")
            
        except Exception as e:
            print("ERRO ao criar arquivo: " + str(e))
            doc.Close(False)
            return False
        
        # Fechar documento
        doc.Close(False)
        print("Documento fechado")
        
        return True
        
    except Exception as e:
        print("ERRO: " + str(e))
        return False

# Executar conversão
if __name__ == "__main__":
    success = convert_rfa_to_obj()
    
    if success:
        print("=== CONVERSAO CONCLUIDA COM SUCESSO! ===")
    else:
        print("=== CONVERSAO FALHOU ===")
        sys.exit(1)
