#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para corrigir f-strings para compatibilidade com IronPython
"""

import re
import os

def fix_fstrings_in_file(file_path):
    """Corrige f-strings em um arquivo para usar formatação tradicional"""
    
    print(f"Corrigindo f-strings em: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Padrão para encontrar f-strings simples
    # f"texto {variavel}" -> "texto " + str(variavel)
    pattern = r'f"([^"]*?)\{([^}]+)\}([^"]*?)"'
    
    def replace_fstring(match):
        before = match.group(1)
        variable = match.group(2)
        after = match.group(3)
        
        result = f'"{before}" + str({variable})'
        if after:
            result += f' + "{after}"'
        
        return result
    
    # Aplicar substituições
    new_content = re.sub(pattern, replace_fstring, content)
    
    # Padrão para f-strings mais complexas com múltiplas variáveis
    # Isso é mais complexo, vamos fazer manualmente para casos específicos
    
    # Salvar arquivo corrigido
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Arquivo corrigido: {file_path}")

def main():
    """Função principal"""
    file_path = "rfa_to_obj_via_project.py"
    
    if os.path.exists(file_path):
        fix_fstrings_in_file(file_path)
        print("✅ Correção concluída!")
    else:
        print(f"❌ Arquivo não encontrado: {file_path}")

if __name__ == "__main__":
    main()
