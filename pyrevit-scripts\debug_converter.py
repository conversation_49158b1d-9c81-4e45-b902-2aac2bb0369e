#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de debug para conversão RFA → OBJ
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

def main():
    try:
        print("=== DEBUG CONVERTER ===")

        # Obter argumentos
        args = sys.argv
        print("Argumentos: " + str(args))

        if len(args) < 2:
            print("ERRO: Arquivo RFA não especificado")
            return 1

        input_file = args[1]
        print("Arquivo RFA: " + input_file)

        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("ERRO: Arquivo não encontrado")
            return 1

        print("Arquivo encontrado")

        # Obter aplicação do Revit
        app = revit.app
        if app is None:
            print("ERRO: Aplicação Revit não disponível")
            return 1

        print("Aplicação Revit obtida: " + app.VersionName)

        # Abrir documento de família
        print("Abrindo documento de família...")
        family_doc = app.OpenDocumentFile(input_file)

        if family_doc is None:
            print("ERRO: Falha ao abrir documento de família")
            return 1

        print("Documento de família aberto: " + family_doc.Title)

        # Verificar se é família
        if not family_doc.IsFamilyDocument:
            print("ERRO: Não é um documento de família")
            family_doc.Close(False)
            return 1

        print("Documento é uma família válida")

        # Fechar documento de família
        family_title = family_doc.Title
        family_doc.Close(False)
        print("Documento de família fechado")

        # Criar novo documento de projeto
        print("Criando documento de projeto...")
        try:
            project_doc = app.NewProjectDocument(UnitSystem.Metric)
            if project_doc is None:
                print("ERRO: Falha ao criar documento de projeto")
                return 1
            print("Documento de projeto criado: " + project_doc.Title)
        except Exception as e:
            print("ERRO ao criar documento de projeto: " + str(e))
            return 1

        # Carregar família no projeto
        print("Carregando família no projeto...")
        try:
            with Transaction(project_doc, "Carregar Família") as trans:
                trans.Start()

                loaded_family = None
                load_result = project_doc.LoadFamily(input_file, loaded_family)

                if load_result and loaded_family is not None:
                    trans.Commit()
                    print("Família carregada: " + loaded_family.Name)
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao carregar família")
                    project_doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao carregar família: " + str(e))
            project_doc.Close(False)
            return 1

        # Inserir instância na origem
        print("Inserindo instância na origem...")
        try:
            # Obter primeiro símbolo da família
            family_symbol_ids = loaded_family.GetFamilySymbolIds()
            if len(family_symbol_ids) == 0:
                print("ERRO: Nenhum símbolo encontrado")
                project_doc.Close(False)
                return 1

            family_symbol_id = list(family_symbol_ids)[0]
            family_symbol = project_doc.GetElement(family_symbol_id)

            with Transaction(project_doc, "Inserir Família") as trans:
                trans.Start()

                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                    project_doc.Regenerate()

                # Criar instância na origem
                origin_point = XYZ(0, 0, 0)
                family_instance = project_doc.Create.NewFamilyInstance(
                    origin_point,
                    family_symbol,
                    StructuralType.NonStructural
                )

                if family_instance is not None:
                    trans.Commit()
                    print("Instância criada: ID " + str(family_instance.Id))
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao criar instância")
                    project_doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao inserir instância: " + str(e))
            project_doc.Close(False)
            return 1

        # Extrair geometria real da instância
        print("Extraindo geometria real da instância...")
        vertices = []
        faces = []
        vertex_count = 0

        try:
            # Coletar todas as instâncias de família
            collector = FilteredElementCollector(project_doc)
            family_instances = collector.OfClass(FamilyInstance).ToElements()

            print("Instâncias encontradas: " + str(len(family_instances)))

            for instance in family_instances:
                print("Processando instância ID: " + str(instance.Id))

                # Obter geometria da instância
                geom = instance.get_Geometry(Options())
                if geom is not None:
                    print("Geometria encontrada para instância")

                    for geom_obj in geom:
                        print("Tipo de geometria: " + str(type(geom_obj)))

                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            print("Sólido encontrado com volume: " + str(geom_obj.Volume))
                            solid_vertices, solid_faces = extract_solid_geometry(geom_obj, vertex_count)
                            vertices.extend(solid_vertices)
                            faces.extend(solid_faces)
                            vertex_count += len(solid_vertices)

                        elif isinstance(geom_obj, GeometryInstance):
                            print("Instância de geometria encontrada")
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    print("Sólido na instância com volume: " + str(inst_obj.Volume))
                                    solid_vertices, solid_faces = extract_solid_geometry(inst_obj, vertex_count)
                                    vertices.extend(solid_vertices)
                                    faces.extend(solid_faces)
                                    vertex_count += len(solid_vertices)
                else:
                    print("Nenhuma geometria encontrada para instância")

            print("Total de vértices extraídos: " + str(len(vertices)))
            print("Total de faces extraídas: " + str(len(faces)))

        except Exception as e:
            print("ERRO ao extrair geometria: " + str(e))

        # Criar arquivo OBJ com geometria real ou placeholder
        output_file = os.path.splitext(input_file)[0] + ".obj"
        print("Criando arquivo OBJ: " + output_file)

        try:
            with open(output_file, 'w') as f:
                f.write("# BIMEX Debug Converter - EM NOME DE JESUS!\n")
                f.write("# Família: " + family_title + "\n")
                f.write("# Conversão via modelo de projeto\n")
                f.write("# Vértices: " + str(len(vertices)) + "\n")
                f.write("# Faces: " + str(len(faces)) + "\n\n")

                if len(vertices) > 0:
                    # Escrever geometria real
                    for vertex in vertices:
                        f.write(vertex + "\n")
                    f.write("\n")
                    for face in faces:
                        f.write(face + "\n")
                else:
                    # Criar cubo básico como placeholder
                    f.write("# Cubo placeholder (geometria não extraída)\n")
                    f.write("v -0.5 -0.5 -0.5\n")
                    f.write("v  0.5 -0.5 -0.5\n")
                    f.write("v  0.5  0.5 -0.5\n")
                    f.write("v -0.5  0.5 -0.5\n")
                    f.write("v -0.5 -0.5  0.5\n")
                    f.write("v  0.5 -0.5  0.5\n")
                    f.write("v  0.5  0.5  0.5\n")
                    f.write("v -0.5  0.5  0.5\n")
                    f.write("\n")
                    f.write("# Faces do cubo\n")
                    f.write("f 1 2 3 4\n")
                    f.write("f 5 8 7 6\n")
                    f.write("f 1 5 6 2\n")
                    f.write("f 2 6 7 3\n")
                    f.write("f 3 7 8 4\n")
                    f.write("f 5 1 4 8\n")

            print("ARQUIVO OBJ CRIADO COM SUCESSO EM NOME DE JESUS!")

            # Verificar tamanho
            file_size = os.path.getsize(output_file)
            print("Tamanho do arquivo: " + str(file_size) + " bytes")

        except Exception as e:
            print("ERRO ao criar arquivo OBJ: " + str(e))
            project_doc.Close(False)
            return 1

        # Fechar documento de projeto
        project_doc.Close(False)
        print("Documento de projeto fechado")

        print("=== DEBUG CONCLUÍDO COM SUCESSO ===")
        return 0

    except Exception as e:
        print("ERRO durante debug: " + str(e))
        return 1

def extract_solid_geometry(solid, vertex_offset):
    """
    Extrai vértices e faces de um sólido
    """
    vertices = []
    faces = []

    try:
        print("Extraindo geometria do sólido...")

        # Obter faces do sólido
        face_count = 0
        for face in solid.Faces:
            face_count += 1
            try:
                print("Processando face " + str(face_count))

                # Triangular a face
                mesh = face.Triangulate()
                if mesh is None:
                    print("Falha ao triangular face")
                    continue

                print("Face triangulada com " + str(mesh.NumTriangles) + " triângulos")

                # Extrair vértices da malha
                face_vertices = []
                for i in range(mesh.NumTriangles):
                    triangle = mesh.get_Triangle(i)

                    for j in range(3):
                        vertex = triangle.get_Vertex(j)
                        # Converter unidades do Revit (pés) para metros
                        x = vertex.X * 0.3048
                        y = vertex.Y * 0.3048
                        z = vertex.Z * 0.3048

                        vertex_str = "v " + str(x) + " " + str(y) + " " + str(z)
                        vertices.append(vertex_str)
                        face_vertices.append(len(vertices) + vertex_offset)

                    # Criar face (OBJ usa índices baseados em 1)
                    if len(face_vertices) >= 3:
                        v1 = face_vertices[-3]
                        v2 = face_vertices[-2]
                        v3 = face_vertices[-1]
                        face_str = "f " + str(v1) + " " + str(v2) + " " + str(v3)
                        faces.append(face_str)

            except Exception as e:
                print("Erro ao processar face: " + str(e))
                continue

        print("Extraídos " + str(len(vertices)) + " vértices e " + str(len(faces)) + " faces")

    except Exception as e:
        print("Erro ao extrair geometria do sólido: " + str(e))

    return vertices, faces

if __name__ == "__main__":
    sys.exit(main())
