#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit para conversão de arquivos RFA para OBJ
Executado via pyRevit CLI dentro do contexto do Revit

Uso via pyRevit CLI:
    pyrevit run pyrevit_export_obj.py --input arquivo.rfa --output arquivo.obj

Este script é executado dentro do Revit e tem acesso completo às APIs
"""

import sys
import os
import argparse
import tempfile
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Importar APIs do Revit (disponíveis quando executado via pyRevit)
try:
    import clr
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    clr.AddReference('RevitServices')

    from Autodesk.Revit.DB import *
    from Autodesk.Revit.ApplicationServices import Application
    from RevitServices.Persistence import DocumentManager
    from RevitServices.Transactions import TransactionManager

    # Verificar se estamos em contexto pyRevit
    try:
        from pyrevit import revit, DB, UI, script
        PYREVIT_CONTEXT = True
        logger.info("✅ Executando em contexto pyRevit")
    except ImportError:
        PYREVIT_CONTEXT = False
        logger.warning("⚠️ pyRevit context não disponível, usando APIs diretas")

    REVIT_AVAILABLE = True
    logger.info("✅ APIs do Revit carregadas com sucesso")

except ImportError as e:
    REVIT_AVAILABLE = False
    logger.error(f"❌ Erro ao importar APIs do Revit: {e}")
    logger.error("Este script deve ser executado via pyRevit CLI")


class PyRevitOBJExporter:
    """Exportador OBJ usando pyRevit e APIs do Revit"""

    def __init__(self):
        self.app = None
        self.doc = None

    def initialize(self):
        """Inicializa conexão com Revit"""
        try:
            if not REVIT_AVAILABLE:
                raise Exception("APIs do Revit não disponíveis")

            if PYREVIT_CONTEXT:
                # Usar pyRevit context
                self.app = revit.app
                logger.info("✅ Usando aplicação pyRevit")
            else:
                # Usar DocumentManager (Dynamo style)
                self.app = DocumentManager.Instance.CurrentUIApplication.Application
                logger.info("✅ Usando DocumentManager")

            return True

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar: {e}")
            return False

    def open_family(self, rfa_path):
        """Abre arquivo de família RFA"""
        try:
            if not os.path.exists(rfa_path):
                raise FileNotFoundError(f"Arquivo não encontrado: {rfa_path}")

            logger.info(f"📂 Abrindo família: {rfa_path}")

            # Abrir documento de família
            self.doc = self.app.OpenDocumentFile(rfa_path)

            if self.doc is None:
                raise Exception("Falha ao abrir documento")

            logger.info(f"✅ Família aberta: {self.doc.Title}")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao abrir família: {e}")
            return False

    def export_to_obj(self, output_path):
        """Exporta família para OBJ"""
        try:
            if self.doc is None:
                raise Exception("Nenhum documento carregado")

            logger.info(f"🔄 Exportando para OBJ: {output_path}")

            # Criar diretório de saída
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Coletar elementos com geometria
            collector = FilteredElementCollector(self.doc)
            elements = collector.WhereElementIsNotElementType().ToElements()

            # Filtrar elementos com geometria 3D
            geometric_elements = []
            for element in elements:
                try:
                    geom = element.get_Geometry(Options())
                    if geom is not None:
                        geometric_elements.append(element)
                except:
                    continue

            logger.info(f"📊 Encontrados {len(geometric_elements)} elementos com geometria")

            if not geometric_elements:
                logger.warning("⚠️ Nenhum elemento geométrico encontrado")
                return self._create_empty_obj(output_path)

            # Extrair geometria e criar OBJ
            return self._extract_geometry_to_obj(geometric_elements, output_path)

        except Exception as e:
            logger.error(f"❌ Erro durante exportação: {e}")
            return False

    def _extract_geometry_to_obj(self, elements, output_path):
        """Extrai geometria dos elementos e cria arquivo OBJ com máxima qualidade"""
        try:
            vertices = []
            faces = []
            vertex_count = 0

            logger.info("🔄 Extraindo geometria dos elementos com máxima qualidade...")

            # Configurar opções de geometria para máxima qualidade
            geom_options = Options()
            geom_options.DetailLevel = ViewDetailLevel.Fine  # Máximo nível de detalhe
            geom_options.ComputeReferences = True  # Computar referências para geometria detalhada
            geom_options.IncludeNonVisibleObjects = True  # Incluir objetos não visíveis

            logger.info("✅ Configurações de alta qualidade aplicadas:")
            logger.info("   - DetailLevel: Fine (máximo detalhe)")
            logger.info("   - ComputeReferences: True")
            logger.info("   - IncludeNonVisibleObjects: True")

            for element in elements:
                try:
                    geom = element.get_Geometry(geom_options)
                    if geom is None:
                        continue

                    for geom_obj in geom:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            # Processar sólido com máxima qualidade
                            solid_vertices, solid_faces = self._process_solid_high_quality(geom_obj, vertex_count)
                            vertices.extend(solid_vertices)
                            faces.extend(solid_faces)
                            vertex_count += len(solid_vertices)

                        elif isinstance(geom_obj, GeometryInstance):
                            # Processar instância de geometria com máxima qualidade
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    solid_vertices, solid_faces = self._process_solid_high_quality(inst_obj, vertex_count)
                                    vertices.extend(solid_vertices)
                                    faces.extend(solid_faces)
                                    vertex_count += len(solid_vertices)

                        elif isinstance(geom_obj, Mesh):
                            # Processar meshes diretamente para preservar detalhes
                            mesh_vertices, mesh_faces = self._process_mesh_high_quality(geom_obj, vertex_count)
                            vertices.extend(mesh_vertices)
                            faces.extend(mesh_faces)
                            vertex_count += len(mesh_vertices)

                except Exception as e:
                    logger.warning(f"⚠️ Erro ao processar elemento {element.Id}: {e}")
                    continue

            # Escrever arquivo OBJ com informações de alta qualidade
            with open(output_path, 'w') as f:
                f.write("# Arquivo OBJ exportado pelo BIMEX pyRevit Converter - ALTA QUALIDADE\n")
                f.write(f"# Família: {self.doc.Title}\n")
                f.write(f"# Elementos processados: {len(elements)}\n")
                f.write(f"# Vértices: {len(vertices)}\n")
                f.write(f"# Faces: {len(faces)}\n")
                f.write("# Configurações de qualidade aplicadas:\n")
                f.write("#   - DetailLevel: Fine (máximo detalhe)\n")
                f.write("#   - ComputeReferences: True\n")
                f.write("#   - IncludeNonVisibleObjects: True\n")
                f.write("#   - Precisão de vértices: 8 casas decimais\n")
                f.write("#   - Preservação completa da geometria original\n\n")

                # Escrever vértices
                for vertex in vertices:
                    f.write(vertex + "\n")

                f.write("\n")

                # Escrever faces
                for face in faces:
                    f.write(face + "\n")

            logger.info(f"✅ OBJ criado: {len(vertices)} vértices, {len(faces)} faces")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao extrair geometria: {e}")
            return False

    def _process_solid(self, solid, vertex_offset):
        """Processa um sólido e retorna vértices e faces"""
        vertices = []
        faces = []

        try:
            # Triangular todas as faces do sólido
            for face in solid.Faces:
                mesh = face.Triangulate()
                if mesh is not None:
                    # Adicionar vértices do mesh
                    for i in range(mesh.NumTriangles):
                        triangle = mesh.get_Triangle(i)
                        for j in range(3):
                            vertex = triangle.get_Vertex(j)
                            # Converter unidades do Revit (pés) para metros
                            x = vertex.X * 0.3048
                            y = vertex.Y * 0.3048
                            z = vertex.Z * 0.3048
                            vertices.append(f"v {x:.6f} {y:.6f} {z:.6f}")

                    # Adicionar faces (triângulos)
                    for i in range(mesh.NumTriangles):
                        v1 = vertex_offset + (i * 3) + 1
                        v2 = vertex_offset + (i * 3) + 2
                        v3 = vertex_offset + (i * 3) + 3
                        faces.append(f"f {v1} {v2} {v3}")

        except Exception as e:
            logger.warning(f"⚠️ Erro ao processar sólido: {e}")

        return vertices, faces

    def _process_solid_high_quality(self, solid, vertex_offset):
        """Processa um sólido com máxima qualidade e preservação de detalhes"""
        vertices = []
        faces = []

        try:
            logger.debug(f"🔍 Processando sólido com {solid.Faces.Size} faces")

            # Triangular todas as faces do sólido com máxima qualidade
            for face in solid.Faces:
                try:
                    # Usar triangulação padrão (sem parâmetros de simplificação)
                    mesh = face.Triangulate()
                    if mesh is not None and mesh.NumTriangles > 0:
                        logger.debug(f"   Face triangulada: {mesh.NumTriangles} triângulos")

                        # Adicionar vértices do mesh com máxima precisão
                        for i in range(mesh.NumTriangles):
                            triangle = mesh.get_Triangle(i)
                            for j in range(3):
                                vertex = triangle.get_Vertex(j)
                                # Converter unidades do Revit (pés) para metros com alta precisão
                                x = vertex.X * 0.3048
                                y = vertex.Y * 0.3048
                                z = vertex.Z * 0.3048
                                # Usar 8 casas decimais para máxima precisão
                                vertices.append(f"v {x:.8f} {y:.8f} {z:.8f}")

                        # Adicionar faces (triângulos) preservando ordem correta
                        for i in range(mesh.NumTriangles):
                            v1 = vertex_offset + (i * 3) + 1
                            v2 = vertex_offset + (i * 3) + 2
                            v3 = vertex_offset + (i * 3) + 3
                            faces.append(f"f {v1} {v2} {v3}")
                    else:
                        logger.debug("   Face não pôde ser triangulada ou está vazia")

                except Exception as e:
                    logger.debug(f"   Erro ao triangular face: {e}")
                    continue

        except Exception as e:
            logger.warning(f"⚠️ Erro ao processar sólido com alta qualidade: {e}")

        logger.debug(f"✅ Sólido processado: {len(vertices)} vértices, {len(faces)} faces")
        return vertices, faces

    def _process_mesh_high_quality(self, mesh, vertex_offset):
        """Processa um mesh diretamente com máxima qualidade"""
        vertices = []
        faces = []

        try:
            logger.debug(f"🔍 Processando mesh direto com {mesh.NumTriangles} triângulos")

            # Processar triângulos do mesh diretamente
            for i in range(mesh.NumTriangles):
                triangle = mesh.get_Triangle(i)

                # Adicionar vértices com máxima precisão
                for j in range(3):
                    vertex = triangle.get_Vertex(j)
                    # Converter unidades do Revit (pés) para metros com alta precisão
                    x = vertex.X * 0.3048
                    y = vertex.Y * 0.3048
                    z = vertex.Z * 0.3048
                    # Usar 8 casas decimais para máxima precisão
                    vertices.append(f"v {x:.8f} {y:.8f} {z:.8f}")

                # Adicionar face
                v1 = vertex_offset + (i * 3) + 1
                v2 = vertex_offset + (i * 3) + 2
                v3 = vertex_offset + (i * 3) + 3
                faces.append(f"f {v1} {v2} {v3}")

        except Exception as e:
            logger.warning(f"⚠️ Erro ao processar mesh direto: {e}")

        logger.debug(f"✅ Mesh processado: {len(vertices)} vértices, {len(faces)} faces")
        return vertices, faces

    def _create_empty_obj(self, output_path):
        """Cria arquivo OBJ vazio para famílias sem geometria"""
        try:
            with open(output_path, 'w') as f:
                f.write("# Arquivo OBJ - Família sem geometria 3D\n")
                f.write(f"# Família: {self.doc.Title}\n")
                f.write("# Nenhum elemento geométrico encontrado\n")

            logger.info("✅ Arquivo OBJ vazio criado")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao criar OBJ vazio: {e}")
            return False

    def close_document(self):
        """Fecha documento sem salvar"""
        try:
            if self.doc is not None:
                self.doc.Close(False)  # False = não salvar
                logger.info("📄 Documento fechado")
        except Exception as e:
            logger.warning(f"⚠️ Erro ao fechar documento: {e}")


def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Converte RFA para OBJ usando pyRevit"
    )
    parser.add_argument('--input', '-i', required=True, help="Arquivo RFA")
    parser.add_argument('--output', '-o', required=True, help="Arquivo OBJ")
    parser.add_argument('--verbose', '-v', action='store_true', help="Logs verbosos")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Verificar se APIs estão disponíveis
    if not REVIT_AVAILABLE:
        logger.error("❌ APIs do Revit não disponíveis")
        logger.error("Execute via: pyrevit run pyrevit_export_obj.py --input arquivo.rfa --output arquivo.obj")
        return 1

    # Executar conversão
    exporter = PyRevitOBJExporter()

    try:
        # Inicializar
        if not exporter.initialize():
            return 1

        # Abrir família
        if not exporter.open_family(args.input):
            return 1

        # Exportar para OBJ
        if not exporter.export_to_obj(args.output):
            return 1

        logger.info("🎉 Conversão concluída com sucesso!")
        return 0

    except Exception as e:
        logger.error(f"❌ Erro durante conversão: {e}")
        return 1

    finally:
        # Limpar recursos
        exporter.close_document()


if __name__ == "__main__":
    sys.exit(main())
