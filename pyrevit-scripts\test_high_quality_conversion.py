#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Teste para validar as melhorias de alta qualidade na conversão Revit para OBJ

Este script testa se as configurações de alta qualidade estão sendo aplicadas corretamente:
- DetailLevel.Fine para máximo detalhe
- ComputeReferences = True para geometria detalhada
- IncludeNonVisibleObjects = True para objetos completos
- Precisão de 8 casas decimais nos vértices
- Processamento de meshes diretos
"""

import os
import sys
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_obj_file_quality(obj_file_path):
    """Testa a qualidade do arquivo OBJ gerado"""
    if not os.path.exists(obj_file_path):
        logger.error(f"❌ Arquivo OBJ não encontrado: {obj_file_path}")
        return False
    
    logger.info(f"🔍 Analisando arquivo OBJ: {obj_file_path}")
    
    with open(obj_file_path, 'r') as f:
        content = f.read()
    
    # Verificar cabeçalho de alta qualidade
    high_quality_indicators = [
        "ALTA QUALIDADE",
        "DetailLevel: Fine",
        "ComputeReferences: True",
        "IncludeNonVisibleObjects: True",
        "Precisão de vértices: 8 casas decimais"
    ]
    
    quality_score = 0
    for indicator in high_quality_indicators:
        if indicator in content:
            quality_score += 1
            logger.info(f"✅ Encontrado: {indicator}")
        else:
            logger.warning(f"⚠️ Não encontrado: {indicator}")
    
    # Analisar vértices
    lines = content.split('\n')
    vertex_lines = [line for line in lines if line.startswith('v ')]
    face_lines = [line for line in lines if line.startswith('f ')]
    
    logger.info(f"📊 Estatísticas do arquivo:")
    logger.info(f"   - Vértices: {len(vertex_lines)}")
    logger.info(f"   - Faces: {len(face_lines)}")
    
    # Verificar precisão dos vértices
    if vertex_lines:
        sample_vertex = vertex_lines[0]
        coords = sample_vertex.split()[1:4]  # Pegar x, y, z
        
        precision_check = True
        for coord in coords:
            if '.' in coord:
                decimal_places = len(coord.split('.')[1])
                if decimal_places >= 6:  # Pelo menos 6 casas decimais (melhor que antes)
                    logger.info(f"✅ Precisão adequada: {decimal_places} casas decimais")
                else:
                    logger.warning(f"⚠️ Precisão baixa: {decimal_places} casas decimais")
                    precision_check = False
    
    # Calcular score final
    total_score = quality_score / len(high_quality_indicators) * 100
    
    logger.info(f"🎯 Score de qualidade: {total_score:.1f}%")
    
    if total_score >= 80:
        logger.info("🎉 EXCELENTE: Configurações de alta qualidade aplicadas com sucesso!")
        return True
    elif total_score >= 60:
        logger.info("👍 BOM: Maioria das configurações de alta qualidade aplicadas")
        return True
    else:
        logger.warning("⚠️ ATENÇÃO: Poucas configurações de alta qualidade detectadas")
        return False

def compare_with_previous_version(old_obj_path, new_obj_path):
    """Compara arquivo OBJ antigo com novo para mostrar melhorias"""
    if not os.path.exists(old_obj_path) or not os.path.exists(new_obj_path):
        logger.warning("⚠️ Não é possível comparar - arquivos não encontrados")
        return
    
    logger.info("🔄 Comparando versões...")
    
    # Analisar arquivo antigo
    with open(old_obj_path, 'r') as f:
        old_content = f.read()
    old_vertices = len([line for line in old_content.split('\n') if line.startswith('v ')])
    old_faces = len([line for line in old_content.split('\n') if line.startswith('f ')])
    
    # Analisar arquivo novo
    with open(new_obj_path, 'r') as f:
        new_content = f.read()
    new_vertices = len([line for line in new_content.split('\n') if line.startswith('v ')])
    new_faces = len([line for line in new_content.split('\n') if line.startswith('f ')])
    
    logger.info(f"📈 Comparação de qualidade:")
    logger.info(f"   Vértices: {old_vertices} → {new_vertices} ({new_vertices - old_vertices:+d})")
    logger.info(f"   Faces: {old_faces} → {new_faces} ({new_faces - old_faces:+d})")
    
    if new_vertices > old_vertices:
        logger.info("✅ MELHORIA: Mais vértices = maior detalhe geométrico")
    if new_faces > old_faces:
        logger.info("✅ MELHORIA: Mais faces = maior resolução da superfície")

def main():
    """Função principal de teste"""
    logger.info("🚀 Iniciando teste de qualidade da conversão Revit para OBJ")
    
    # Arquivo de teste
    test_obj = "test_high_quality_output.obj"
    
    if os.path.exists(test_obj):
        logger.info(f"📁 Arquivo de teste encontrado: {test_obj}")
        test_obj_file_quality(test_obj)
    else:
        logger.warning(f"⚠️ Arquivo de teste não encontrado: {test_obj}")
        logger.info("💡 Execute primeiro a conversão com:")
        logger.info("   python bimex_converter.py input.rfa output.obj")
    
    logger.info("✅ Teste concluído!")

if __name__ == "__main__":
    main()
