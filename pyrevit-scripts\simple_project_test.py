#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Teste simples para criar projeto e inserir família
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

print("=== TESTE SIMPLES - CRIAR PROJETO E INSERIR FAMÍLIA ===")

def main():
    try:
        # Obter argumentos
        args = sys.argv
        if len(args) < 2:
            print("ERRO: Arquivo RFA não especificado")
            return 1
        
        input_file = args[1]
        print("Arquivo RFA: " + input_file)
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("ERRO: Arquivo não encontrado")
            return 1
        
        # Obter aplicação do Revit
        app = revit.app
        if app is None:
            print("ERRO: Aplicação Revit não disponível")
            return 1
        
        print("Aplicação Revit obtida: " + app.VersionName)
        
        # Criar novo documento de projeto
        print("Criando documento de projeto...")
        try:
            doc = app.NewProjectDocument(UnitSystem.Metric)
            if doc is None:
                print("ERRO: Falha ao criar documento")
                return 1
            print("Documento criado: " + doc.Title)
        except Exception as e:
            print("ERRO ao criar documento: " + str(e))
            return 1
        
        # Carregar família
        print("Carregando família...")
        try:
            with Transaction(doc, "Carregar Família") as trans:
                trans.Start()
                
                loaded_family = None
                load_result = doc.LoadFamily(input_file, loaded_family)
                
                if load_result and loaded_family is not None:
                    trans.Commit()
                    print("Família carregada: " + loaded_family.Name)
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao carregar família")
                    doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao carregar família: " + str(e))
            doc.Close(False)
            return 1
        
        # Inserir instância na origem
        print("Inserindo instância na origem...")
        try:
            # Obter primeiro símbolo da família
            family_symbol_ids = loaded_family.GetFamilySymbolIds()
            if len(family_symbol_ids) == 0:
                print("ERRO: Nenhum símbolo encontrado")
                doc.Close(False)
                return 1
            
            family_symbol_id = list(family_symbol_ids)[0]
            family_symbol = doc.GetElement(family_symbol_id)
            
            with Transaction(doc, "Inserir Família") as trans:
                trans.Start()
                
                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                    doc.Regenerate()
                
                # Criar instância na origem
                origin_point = XYZ(0, 0, 0)
                family_instance = doc.Create.NewFamilyInstance(
                    origin_point,
                    family_symbol,
                    StructuralType.NonStructural
                )
                
                if family_instance is not None:
                    trans.Commit()
                    print("Instância criada: ID " + str(family_instance.Id))
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao criar instância")
                    doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao inserir instância: " + str(e))
            doc.Close(False)
            return 1
        
        # Verificar geometria
        print("Verificando geometria...")
        try:
            collector = FilteredElementCollector(doc)
            family_instances = collector.OfClass(FamilyInstance).ToElements()
            print("Instâncias encontradas: " + str(len(family_instances)))
            
            for instance in family_instances:
                geom = instance.get_Geometry(Options())
                if geom is not None:
                    solid_count = 0
                    for geom_obj in geom:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            solid_count += 1
                        elif isinstance(geom_obj, GeometryInstance):
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    solid_count += 1
                    print("Sólidos encontrados: " + str(solid_count))
        except Exception as e:
            print("ERRO ao verificar geometria: " + str(e))
        
        # Fechar documento
        doc.Close(False)
        print("Documento fechado")
        
        print("=== TESTE CONCLUÍDO COM SUCESSO ===")
        return 0
        
    except Exception as e:
        print("ERRO durante teste: " + str(e))
        return 1

if __name__ == "__main__":
    sys.exit(main())
