#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Teste para validar a conversão FBX do BIMEX

Este script testa se a conversão para FBX está funcionando corretamente
e preservando a geometria com máxima fidelidade.
"""

import os
import sys
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fbx_conversion():
    """Testa a conversão FBX"""
    logger.info("🚀 Iniciando teste de conversão FBX")
    
    # Arquivo de teste
    test_rfa = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
    output_fbx = "test_fbx_output.fbx"
    
    if not os.path.exists(test_rfa):
        logger.error(f"❌ Arquivo de teste não encontrado: {test_rfa}")
        return False
    
    logger.info(f"📁 Arquivo de teste: {test_rfa}")
    logger.info(f"📁 Arquivo de saída: {output_fbx}")
    
    try:
        # Importar função de conversão
        from bimex_converter import convert_rfa_to_fbx
        
        # Executar conversão
        logger.info("🔄 Executando conversão RFA → FBX...")
        result = convert_rfa_to_fbx(test_rfa, output_fbx)
        
        # Analisar resultado
        logger.info("📊 Resultado da conversão:")
        logger.info(f"  ✅ Sucesso: {result.get('success', False)}")
        logger.info(f"  📁 Arquivo entrada: {result.get('input_path', 'N/A')}")
        logger.info(f"  📁 Arquivo saída: {result.get('output_path', 'N/A')}")
        logger.info(f"  📏 Tamanho saída: {result.get('output_size', 0)} bytes")
        logger.info(f"  🎯 Formato: {result.get('format', 'N/A')}")
        logger.info(f"  ⭐ Qualidade: {result.get('quality', 'N/A')}")
        
        if result.get('features'):
            logger.info("  🎨 Recursos preservados:")
            for feature in result.get('features', []):
                logger.info(f"    - {feature}")
        
        if result.get('success'):
            # Verificar se arquivo foi criado
            if os.path.exists(output_fbx):
                file_size = os.path.getsize(output_fbx)
                logger.info(f"✅ Arquivo FBX criado com sucesso!")
                logger.info(f"📏 Tamanho: {file_size} bytes")
                
                # Analisar conteúdo do arquivo
                analyze_fbx_file(output_fbx)
                return True
            else:
                logger.error("❌ Arquivo FBX não foi criado")
                return False
        else:
            logger.error(f"❌ Conversão falhou: {result.get('error', 'Erro desconhecido')}")
            if result.get('stderr'):
                logger.error(f"STDERR: {result.get('stderr')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro durante teste: {e}")
        return False

def analyze_fbx_file(fbx_path):
    """Analisa o conteúdo do arquivo FBX"""
    try:
        logger.info("🔍 Analisando arquivo FBX...")
        
        with open(fbx_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar indicadores de qualidade
        quality_indicators = [
            ("FBX Header", "FBX" in content),
            ("Geometria", "Geometry" in content or "Mesh" in content),
            ("Vértices", "Vertices" in content or "vertices" in content),
            ("Faces/Polígonos", "PolygonVertexIndex" in content or "faces" in content),
            ("Materiais", "Material" in content),
            ("Texturas", "Texture" in content),
            ("Normais", "Normal" in content),
            ("BIMEX Creator", "BIMEX" in content)
        ]
        
        logger.info("📋 Análise do conteúdo FBX:")
        for indicator, found in quality_indicators:
            status = "✅" if found else "❌"
            logger.info(f"  {status} {indicator}: {'Encontrado' if found else 'Não encontrado'}")
        
        # Contar linhas
        lines = content.split('\n')
        logger.info(f"📄 Total de linhas: {len(lines)}")
        
        # Verificar se é arquivo real ou placeholder
        if "placeholder" in content.lower() or "basic" in content.lower():
            logger.warning("⚠️ ATENÇÃO: Arquivo parece ser um placeholder")
            logger.info("💡 Para conversão real, certifique-se de que pyRevit está configurado")
        else:
            logger.info("✅ Arquivo parece conter geometria real")
            
    except Exception as e:
        logger.error(f"❌ Erro ao analisar arquivo FBX: {e}")

def compare_with_obj():
    """Compara vantagens do FBX sobre OBJ"""
    logger.info("📊 Vantagens do FBX sobre OBJ:")
    logger.info("  ✅ Preserva materiais e texturas")
    logger.info("  ✅ Mantém superfícies NURBS")
    logger.info("  ✅ Suporte a hierarquias complexas")
    logger.info("  ✅ Melhor fidelidade geométrica")
    logger.info("  ✅ Metadados e propriedades")
    logger.info("  ✅ Animações (se aplicável)")
    logger.info("  ✅ Múltiplos objetos em um arquivo")

def main():
    """Função principal"""
    logger.info("=" * 60)
    logger.info("🎯 TESTE DE CONVERSÃO FBX - BIMEX")
    logger.info("=" * 60)
    
    # Mostrar vantagens do FBX
    compare_with_obj()
    logger.info("")
    
    # Executar teste
    success = test_fbx_conversion()
    
    logger.info("")
    logger.info("=" * 60)
    if success:
        logger.info("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        logger.info("✨ Conversão FBX funcionando corretamente")
    else:
        logger.info("❌ TESTE FALHOU")
        logger.info("💡 Verifique a configuração do pyRevit")
    logger.info("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
