#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para testar a conversão do arquivo específico
"""

import os
import sys
import subprocess
import time

def test_conversion():
    """Testa a conversão do arquivo específico"""
    
    print("=== TESTE DE CONVERSÃO RFA → OBJ VIA MODELO DE PROJETO ===")
    
    # Arquivo de teste
    test_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
    
    print(f"🔍 Verificando arquivo: {test_file}")
    
    # Verificar se arquivo existe
    if not os.path.exists(test_file):
        print(f"❌ ERRO: Arquivo não encontrado: {test_file}")
        print("📁 Listando arquivos no diretório Downloads:")
        downloads_dir = r"C:\Users\<USER>\Downloads"
        try:
            files = os.listdir(downloads_dir)
            rfa_files = [f for f in files if f.lower().endswith('.rfa')]
            if rfa_files:
                print("📄 Arquivos .rfa encontrados:")
                for f in rfa_files:
                    print(f"  - {f}")
            else:
                print("  Nenhum arquivo .rfa encontrado")
        except Exception as e:
            print(f"  Erro ao listar diretório: {e}")
        return False
    
    print(f"✅ Arquivo encontrado: {test_file}")
    
    # Verificar tamanho do arquivo
    try:
        file_size = os.path.getsize(test_file)
        print(f"📏 Tamanho do arquivo: {file_size:,} bytes")
    except Exception as e:
        print(f"⚠️ Erro ao obter tamanho: {e}")
    
    # Caminho do script conversor
    script_path = os.path.join("pyrevit-scripts", "rfa_to_obj_via_project_ironpython.py")
    
    if not os.path.exists(script_path):
        print(f"❌ ERRO: Script conversor não encontrado: {script_path}")
        return False
    
    print(f"✅ Script conversor encontrado: {script_path}")
    
    # Executar conversão
    print("🚀 Iniciando conversão...")
    
    try:
        # Comando pyRevit
        pyrevit_exe = r"C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\pyrevit.exe"
        
        if not os.path.exists(pyrevit_exe):
            print(f"❌ ERRO: pyRevit não encontrado: {pyrevit_exe}")
            return False
        
        print(f"✅ pyRevit encontrado: {pyrevit_exe}")
        
        # Preparar comando
        cmd = [
            pyrevit_exe,
            'run',
            script_path,
            test_file,
            '--revit=2024'
        ]
        
        print("📋 Comando a ser executado:")
        print(" ".join(f'"{arg}"' if ' ' in arg else arg for arg in cmd))
        
        # Executar processo
        print("⏳ Executando conversão (pode demorar alguns minutos)...")
        start_time = time.time()
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300,  # 5 minutos
            cwd=os.getcwd()
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ Tempo de execução: {duration:.2f} segundos")
        print(f"📊 Return code: {result.returncode}")
        
        if result.stdout:
            print("📝 STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ STDERR:")
            print(result.stderr)
        
        # Verificar se arquivo OBJ foi criado
        obj_file = os.path.splitext(test_file)[0] + ".obj"
        
        print(f"🔍 Verificando arquivo OBJ: {obj_file}")
        
        if os.path.exists(obj_file):
            file_size = os.path.getsize(obj_file)
            print(f"✅ Arquivo OBJ criado com sucesso!")
            print(f"📁 Localização: {obj_file}")
            print(f"📏 Tamanho: {file_size:,} bytes")
            
            # Mostrar primeiras linhas do arquivo
            print("📄 Primeiras 15 linhas do arquivo OBJ:")
            try:
                with open(obj_file, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f):
                        if i >= 15:  # Mostrar apenas 15 linhas
                            break
                        print(f"  {i+1:2d}: {line.rstrip()}")
                        
                # Contar vértices e faces
                with open(obj_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    vertex_count = content.count('\nv ')
                    face_count = content.count('\nf ')
                    print(f"📊 Estatísticas do OBJ:")
                    print(f"  - Vértices: {vertex_count}")
                    print(f"  - Faces: {face_count}")
                    
            except Exception as e:
                print(f"  ❌ Erro ao ler arquivo: {e}")
            
            return True
        else:
            print("❌ Arquivo OBJ não foi criado")
            
            # Verificar se há arquivos temporários ou logs
            temp_dir = os.path.dirname(test_file)
            print(f"🔍 Verificando arquivos temporários em: {temp_dir}")
            try:
                files = os.listdir(temp_dir)
                temp_files = [f for f in files if 'temp' in f.lower() or 'log' in f.lower()]
                if temp_files:
                    print("📄 Arquivos temporários encontrados:")
                    for f in temp_files:
                        print(f"  - {f}")
            except Exception as e:
                print(f"  Erro ao verificar arquivos temporários: {e}")
            
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ ERRO: Timeout - processo demorou mais de 5 minutos")
        return False
    except Exception as e:
        print(f"❌ ERRO durante execução: {e}")
        return False

def main():
    """Função principal"""
    success = test_conversion()
    
    if success:
        print("\n🎉 === TESTE CONCLUÍDO COM SUCESSO! ===")
        print("✅ A conversão RFA → OBJ via modelo de projeto funcionou corretamente!")
        return 0
    else:
        print("\n❌ === TESTE FALHOU ===")
        print("❌ A conversão não foi bem-sucedida. Verifique os logs acima.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
