#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit para conversão RFA → OBJ via Modelo de Projeto
Cria um modelo Revit, insere a família na origem e converte para OBJ
Compatível com IronPython (sem f-strings)
Testado e otimizado para funcionar com pyRevit e Revit 2024
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

print("=== BIMEX RFA to OBJ Converter - Versão com Modelo de Projeto (IronPython) ===")

def convert_rfa_to_obj(input_file, output_file=None):
    """
    Converte arquivo RFA para OBJ criando um modelo de projeto e inserindo a família na origem

    Args:
        input_file (str): Caminho para o arquivo .rfa
        output_file (str): Caminho para o arquivo .obj (opcional)

    Returns:
        bool: True se conversão foi bem-sucedida
    """
    family_doc = None
    project_doc = None

    try:
        print("🚀 Iniciando conversão de: " + input_file)

        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("❌ ERRO: Arquivo não encontrado: " + input_file)
            return False

        # Gerar nome do arquivo OBJ se não fornecido
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = base_name + ".obj"

        print("📤 Arquivo de saída: " + output_file)

        # Obter aplicação do Revit através do pyRevit
        app = revit.app
        if app is None:
            print("❌ ERRO: Não foi possível obter a aplicação do Revit")
            return False

        print("✅ Aplicação Revit obtida com sucesso")

        # ETAPA 1: Abrir documento de família para verificação
        print("📂 Abrindo documento de família para verificação...")
        family_doc = app.OpenDocumentFile(input_file)

        if family_doc is None:
            print("❌ ERRO: Falha ao abrir documento de família")
            return False

        print("✅ Documento de família aberto: " + family_doc.Title)

        # Verificar se é um documento de família
        if not family_doc.IsFamilyDocument:
            print("❌ ERRO: O arquivo não é um documento de família válido")
            family_doc.Close(False)
            return False

        # Fechar documento de família (será carregado no projeto)
        family_title = family_doc.Title
        family_doc.Close(False)
        family_doc = None
        print("📄 Documento de família fechado")

        # ETAPA 2: Criar novo documento de projeto
        print("🏗️ Criando novo documento de projeto...")
        project_doc = create_new_project_document(app)

        if project_doc is None:
            print("❌ ERRO: Falha ao criar documento de projeto")
            return False

        print("✅ Documento de projeto criado: " + project_doc.Title)

        # ETAPA 3: Carregar família no projeto
        print("📥 Carregando família no projeto...")
        loaded_family = load_family_into_project(project_doc, input_file)

        if loaded_family is None:
            print("❌ ERRO: Falha ao carregar família no projeto")
            project_doc.Close(False)
            return False

        print("✅ Família carregada: " + loaded_family.Name)

        # ETAPA 4: Inserir instância da família na origem
        print("📍 Inserindo instância da família na origem...")
        family_instance = insert_family_at_origin(project_doc, loaded_family)

        if family_instance is None:
            print("❌ ERRO: Falha ao inserir instância da família")
            project_doc.Close(False)
            return False

        print("✅ Instância inserida na origem: " + str(family_instance.Id))

        # ETAPA 5: Processar geometria do modelo e criar arquivo OBJ
        print("🔄 Processando geometria do modelo...")
        success = process_project_geometry_to_obj(project_doc, output_file, family_title)

        # ETAPA 6: Fechar documento de projeto
        project_doc.Close(False)
        project_doc = None
        print("📄 Documento de projeto fechado")

        return success

    except Exception as e:
        print("❌ ERRO durante conversão: " + str(e))

        # Limpar recursos em caso de erro
        try:
            if family_doc is not None:
                family_doc.Close(False)
        except:
            pass
        try:
            if project_doc is not None:
                project_doc.Close(False)
        except:
            pass

        return False

def create_new_project_document(app):
    """
    Cria um novo documento de projeto Revit

    Args:
        app: Aplicação do Revit

    Returns:
        Document: Novo documento de projeto ou None se falhar
    """
    try:
        # Tentar criar documento com template padrão
        print("  🔧 Tentando criar documento com template padrão...")

        # Primeiro, tentar sem template (documento em branco)
        doc = app.NewProjectDocument(UnitSystem.Metric)

        if doc is not None:
            print("  ✅ Documento criado com template métrico")
            return doc

        # Se falhar, tentar com template imperial
        doc = app.NewProjectDocument(UnitSystem.Imperial)

        if doc is not None:
            print("  ✅ Documento criado com template imperial")
            return doc

        print("  ❌ Falha ao criar documento com templates padrão")
        return None

    except Exception as e:
        print("  ❌ Erro ao criar documento de projeto: " + str(e))
        return None

def load_family_into_project(doc, family_file_path):
    """
    Carrega uma família no documento de projeto

    Args:
        doc: Documento de projeto
        family_file_path: Caminho para o arquivo .rfa

    Returns:
        Family: Família carregada ou None se falhar
    """
    try:
        with Transaction(doc, "Carregar Família") as trans:
            trans.Start()

            # Carregar família
            loaded_family = None
            load_result = doc.LoadFamily(family_file_path, loaded_family)

            if load_result and loaded_family is not None:
                trans.Commit()
                print("  ✅ Família carregada com sucesso: " + loaded_family.Name)
                return loaded_family
            else:
                trans.RollBack()
                print("  ❌ Falha ao carregar família")
                return None

    except Exception as e:
        print("  ❌ Erro ao carregar família: " + str(e))
        return None

def insert_family_at_origin(doc, family):
    """
    Insere uma instância da família na origem (0,0,0)

    Args:
        doc: Documento de projeto
        family: Família a ser inserida

    Returns:
        FamilyInstance: Instância criada ou None se falhar
    """
    try:
        # Obter primeiro símbolo da família
        family_symbol_ids = family.GetFamilySymbolIds()
        if len(family_symbol_ids) == 0:
            print("  ❌ Nenhum símbolo encontrado na família")
            return None

        family_symbol_id = list(family_symbol_ids)[0]
        family_symbol = doc.GetElement(family_symbol_id)

        if family_symbol is None:
            print("  ❌ Não foi possível obter símbolo da família")
            return None

        with Transaction(doc, "Inserir Família na Origem") as trans:
            trans.Start()

            # Ativar símbolo se necessário
            if not family_symbol.IsActive:
                family_symbol.Activate()
                doc.Regenerate()

            # Criar instância na origem
            origin_point = XYZ(0, 0, 0)

            # Inserir instância
            family_instance = doc.Create.NewFamilyInstance(
                origin_point,
                family_symbol,
                StructuralType.NonStructural
            )

            if family_instance is not None:
                trans.Commit()
                print("  ✅ Instância inserida na origem: ID " + str(family_instance.Id))
                return family_instance
            else:
                trans.RollBack()
                print("  ❌ Falha ao criar instância da família")
                return None

    except Exception as e:
        print("  ❌ Erro ao inserir família na origem: " + str(e))
        return None

def process_project_geometry_to_obj(doc, output_file, family_title):
    """
    Processa geometria do documento de projeto e cria arquivo OBJ

    Args:
        doc: Documento de projeto
        output_file: Caminho do arquivo OBJ
        family_title: Título da família original

    Returns:
        bool: True se processamento foi bem-sucedido
    """
    try:
        print("  🔍 Coletando elementos com geometria do projeto...")

        # Coletar todas as instâncias de família
        collector = FilteredElementCollector(doc)
        family_instances = collector.OfClass(FamilyInstance).ToElements()

        print("  📊 Instâncias de família encontradas: " + str(len(family_instances)))

        if len(family_instances) == 0:
            print("  ⚠️ Nenhuma instância de família encontrada")
            create_empty_obj(output_file, family_title)
            return True

        # Filtrar elementos com geometria válida
        geometric_elements = []
        for instance in family_instances:
            try:
                geom = instance.get_Geometry(Options())
                if geom is not None:
                    # Verificar se tem geometria sólida
                    has_solid = False
                    for geom_obj in geom:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            has_solid = True
                            break
                        elif isinstance(geom_obj, GeometryInstance):
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    has_solid = True
                                    break
                            if has_solid:
                                break

                    if has_solid:
                        geometric_elements.append(instance)

            except Exception as e:
                continue

        print("  ✅ Elementos com geometria válida: " + str(len(geometric_elements)))

        if len(geometric_elements) == 0:
            print("  ⚠️ Nenhum elemento com geometria sólida encontrado")
            create_empty_obj(output_file, family_title)
            return True

        # Processar geometria e criar arquivo OBJ
        return process_geometry_to_obj(geometric_elements, output_file, family_title)

    except Exception as e:
        print("  ❌ Erro ao processar geometria do projeto: " + str(e))
        return False

def process_geometry_to_obj(elements, output_file, doc_title):
    """
    Processa geometria dos elementos e cria arquivo OBJ

    Args:
        elements: Lista de elementos com geometria
        output_file: Caminho do arquivo OBJ
        doc_title: Título do documento

    Returns:
        bool: True se processamento foi bem-sucedido
    """
    try:
        vertices = []
        faces = []
        vertex_count = 0

        print("🔄 Processando geometria dos elementos...")

        for i, element in enumerate(elements):
            try:
                print("  Processando elemento " + str(i+1) + "/" + str(len(elements)))

                geom = element.get_Geometry(Options())
                if geom is None:
                    continue

                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        # Processar sólido diretamente
                        solid_vertices, solid_faces = process_solid(geom_obj, vertex_count)
                        vertices.extend(solid_vertices)
                        faces.extend(solid_faces)
                        vertex_count += len(solid_vertices)

                    elif isinstance(geom_obj, GeometryInstance):
                        # Processar instância de geometria
                        inst_geom = geom_obj.GetInstanceGeometry()
                        for inst_obj in inst_geom:
                            if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                solid_vertices, solid_faces = process_solid(inst_obj, vertex_count)
                                vertices.extend(solid_vertices)
                                faces.extend(solid_faces)
                                vertex_count += len(solid_vertices)

            except Exception as e:
                print("  ⚠️ Erro ao processar elemento " + str(i+1) + ": " + str(e))
                continue

        print("📊 Geometria processada:")
        print("  - Vértices: " + str(len(vertices)))
        print("  - Faces: " + str(len(faces)))

        # Criar arquivo OBJ
        return create_obj_file(vertices, faces, output_file, doc_title)

    except Exception as e:
        print("ERRO ao processar geometria: " + str(e))
        return False

def process_solid(solid, vertex_offset):
    """
    Processa um sólido e extrai vértices e faces

    Args:
        solid: Objeto Solid do Revit
        vertex_offset: Offset para numeração dos vértices

    Returns:
        tuple: (lista de vértices, lista de faces)
    """
    vertices = []
    faces = []

    try:
        # Obter faces do sólido
        for face in solid.Faces:
            try:
                # Triangular a face
                mesh = face.Triangulate()
                if mesh is None:
                    continue

                # Extrair vértices da malha
                face_vertices = []
                for i in range(mesh.NumTriangles):
                    triangle = mesh.get_Triangle(i)

                    for j in range(3):
                        vertex = triangle.get_Vertex(j)
                        # Converter unidades do Revit (pés) para metros
                        x = vertex.X * 0.3048
                        y = vertex.Y * 0.3048
                        z = vertex.Z * 0.3048

                        vertex_str = "v " + str(x) + " " + str(y) + " " + str(z)
                        vertices.append(vertex_str)
                        face_vertices.append(len(vertices) + vertex_offset)

                    # Criar face (OBJ usa índices baseados em 1)
                    if len(face_vertices) >= 3:
                        v1 = face_vertices[-3]
                        v2 = face_vertices[-2]
                        v3 = face_vertices[-1]
                        face_str = "f " + str(v1) + " " + str(v2) + " " + str(v3)
                        faces.append(face_str)

            except Exception as e:
                continue

    except Exception as e:
        print("  ⚠️ Erro ao processar sólido: " + str(e))

    return vertices, faces

def create_obj_file(vertices, faces, output_file, doc_title):
    """
    Cria arquivo OBJ com os dados processados

    Args:
        vertices: Lista de vértices
        faces: Lista de faces
        output_file: Caminho do arquivo
        doc_title: Título do documento

    Returns:
        bool: True se arquivo foi criado com sucesso
    """
    try:
        print("📝 Criando arquivo OBJ...")

        with open(output_file, 'w') as f:
            # Cabeçalho
            f.write("# BIMEX RFA to OBJ Converter - Via Modelo de Projeto\n")
            f.write("# Família: " + doc_title + "\n")
            f.write("# Vértices: " + str(len(vertices)) + "\n")
            f.write("# Faces: " + str(len(faces)) + "\n")
            f.write("# Convertido com pyRevit via modelo de projeto\n\n")

            # Escrever vértices
            for vertex in vertices:
                f.write(vertex + "\n")

            f.write("\n")

            # Escrever faces
            for face in faces:
                f.write(face + "\n")

        print("✅ Arquivo OBJ criado com sucesso!")
        print("📁 Localização: " + output_file)

        # Verificar tamanho do arquivo
        file_size = os.path.getsize(output_file)
        print("📏 Tamanho do arquivo: " + str(file_size) + " bytes")

        return True

    except Exception as e:
        print("ERRO ao criar arquivo OBJ: " + str(e))
        return False

def create_empty_obj(output_file, doc_title):
    """
    Cria arquivo OBJ vazio mas válido
    """
    try:
        with open(output_file, 'w') as f:
            f.write("# BIMEX RFA to OBJ Converter - Via Modelo de Projeto\n")
            f.write("# Família: " + doc_title + "\n")
            f.write("# Nenhuma geometria encontrada\n")
            f.write("# Arquivo OBJ vazio\n\n")
            f.write("# Placeholder vertex\n")
            f.write("v 0.0 0.0 0.0\n")

        print("⚠️ Arquivo OBJ vazio criado: " + output_file)
        return True

    except Exception as e:
        print("ERRO ao criar arquivo OBJ vazio: " + str(e))
        return False

def main():
    """Função principal"""
    try:
        print("🚀 Iniciando conversão RFA para OBJ via Modelo de Projeto...")

        # Obter argumentos da linha de comando
        args = sys.argv
        print("📋 Argumentos recebidos: " + str(args))

        if len(args) < 2:
            print("❌ ERRO: Arquivo RFA não especificado")
            print("💡 Uso: pyrevit run rfa_to_obj_via_project_ironpython.py arquivo.rfa [arquivo.obj]")
            return 1

        input_file = args[1]
        output_file = args[2] if len(args) > 2 else None

        print("📥 Arquivo de entrada: " + input_file)
        if output_file:
            print("📤 Arquivo de saída: " + output_file)

        # Executar conversão
        success = convert_rfa_to_obj(input_file, output_file)

        if success:
            print("🎉 === CONVERSÃO CONCLUÍDA COM SUCESSO! ===")
            return 0
        else:
            print("❌ === CONVERSÃO FALHOU ===")
            return 1

    except Exception as e:
        print("❌ ERRO durante execução: " + str(e))
        return 1

# Executar se chamado diretamente
if __name__ == "__main__":
    sys.exit(main())
