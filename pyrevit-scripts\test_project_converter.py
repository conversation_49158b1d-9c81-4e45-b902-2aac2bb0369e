#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de teste para o conversor RFA → OBJ via modelo de projeto
"""

import os
import sys
import subprocess

def test_converter():
    """Testa o conversor com o arquivo de exemplo"""

    print("=== TESTE DO CONVERSOR RFA → OBJ VIA MODELO DE PROJETO ===")

    # Arquivo de teste
    test_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"

    # Verificar se arquivo existe
    if not os.path.exists(test_file):
        print(f"❌ ERRO: Arquivo de teste não encontrado: {test_file}")
        return False

    print(f"✅ Arquivo de teste encontrado: {test_file}")

    # Caminho do script
    script_path = os.path.join(os.path.dirname(__file__), "rfa_to_obj_via_project_ironpython.py")

    if not os.path.exists(script_path):
        print(f"❌ ERRO: Script não encontrado: {script_path}")
        return False

    print(f"✅ Script encontrado: {script_path}")

    # Executar conversão
    print("🚀 Iniciando teste de conversão...")

    try:
        # Comando pyRevit
        pyrevit_exe = r"C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\pyrevit.exe"

        if not os.path.exists(pyrevit_exe):
            print(f"❌ ERRO: pyRevit não encontrado: {pyrevit_exe}")
            return False

        cmd = [
            pyrevit_exe,
            'run',
            script_path,
            test_file,
            '--revit=2024'
        ]

        print("📋 Comando a ser executado:")
        print(" ".join(cmd))

        # Executar processo
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600  # 10 minutos
        )

        print(f"📊 Return code: {result.returncode}")
        print("📝 STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("⚠️ STDERR:")
            print(result.stderr)

        # Verificar se arquivo OBJ foi criado
        obj_file = os.path.splitext(test_file)[0] + ".obj"

        if os.path.exists(obj_file):
            file_size = os.path.getsize(obj_file)
            print(f"✅ Arquivo OBJ criado com sucesso!")
            print(f"📁 Localização: {obj_file}")
            print(f"📏 Tamanho: {file_size} bytes")

            # Mostrar primeiras linhas do arquivo
            print("📄 Primeiras linhas do arquivo OBJ:")
            try:
                with open(obj_file, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f):
                        if i >= 10:  # Mostrar apenas 10 linhas
                            break
                        print(f"  {line.rstrip()}")
            except Exception as e:
                print(f"  ❌ Erro ao ler arquivo: {e}")

            return True
        else:
            print("❌ Arquivo OBJ não foi criado")
            return False

    except subprocess.TimeoutExpired:
        print("❌ ERRO: Timeout - processo demorou mais de 10 minutos")
        return False
    except Exception as e:
        print(f"❌ ERRO durante execução: {e}")
        return False

def main():
    """Função principal"""
    success = test_converter()

    if success:
        print("\n🎉 === TESTE CONCLUÍDO COM SUCESSO! ===")
        return 0
    else:
        print("\n❌ === TESTE FALHOU ===")
        return 1

if __name__ == "__main__":
    sys.exit(main())
