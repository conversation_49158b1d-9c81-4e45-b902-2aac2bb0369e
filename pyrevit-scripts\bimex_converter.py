#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Utilitários de conversão BIMEX para pyRevit
Fornece funcionalidades auxiliares para conversão de famílias Revit

Desenvolvido para o projeto BIMEX Object Market
"""

import os
import sys
import json
import logging
from pathlib import Path

# Configurar logging
logger = logging.getLogger(__name__)

class BimexConverter:
    """Classe utilitária para conversões BIMEX"""

    @staticmethod
    def validate_rfa_file(file_path):
        """Valida se o arquivo é uma família Revit válida"""
        try:
            if not os.path.exists(file_path):
                return False, "Arquivo não encontrado"

            # Verificar extensão
            if not file_path.lower().endswith('.rfa'):
                return False, "Arquivo deve ter extensão .rfa"

            # Verificar tamanho mínimo (arquivo RFA vazio tem pelo menos alguns KB)
            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # Menos de 1KB
                return False, "Arquivo muito pequeno para ser uma família Revit válida"

            # Verificar assinatura do arquivo (arquivos Revit começam com bytes específicos)
            with open(file_path, 'rb') as f:
                header = f.read(16)
                # Famílias Revit têm assinaturas específicas
                if not (header.startswith(b'\xD0\xCF\x11\xE0') or  # OLE Compound Document
                       header.startswith(b'PK')):  # ZIP-based format (newer versions)
                    return False, "Arquivo não parece ser uma família Revit válida"

            return True, "Arquivo válido"

        except Exception as e:
            return False, f"Erro ao validar arquivo: {str(e)}"

    @staticmethod
    def get_family_info(file_path):
        """Extrai informações básicas da família"""
        try:
            info = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size': os.path.getsize(file_path),
                'file_extension': os.path.splitext(file_path)[1].lower(),
                'is_valid': False,
                'validation_message': ''
            }

            # Validar arquivo
            is_valid, message = BimexConverter.validate_rfa_file(file_path)
            info['is_valid'] = is_valid
            info['validation_message'] = message

            return info

        except Exception as e:
            logger.error(f"Erro ao obter informações da família: {e}")
            return None

    @staticmethod
    def prepare_output_path(input_path, output_dir=None):
        """Prepara o caminho de saída para o arquivo FBX"""
        try:
            input_name = os.path.splitext(os.path.basename(input_path))[0]

            if output_dir is None:
                output_dir = os.path.dirname(input_path)

            # Garantir que o diretório existe
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, f"{input_name}.fbx")

            return output_path

        except Exception as e:
            logger.error(f"Erro ao preparar caminho de saída: {e}")
            return None

    @staticmethod
    def create_conversion_report(input_path, output_path, success, error_message=None):
        """Cria relatório de conversão"""
        try:
            report = {
                'timestamp': str(datetime.now()),
                'input_file': input_path,
                'output_file': output_path,
                'success': success,
                'error_message': error_message,
                'input_size': os.path.getsize(input_path) if os.path.exists(input_path) else 0,
                'output_size': os.path.getsize(output_path) if success and os.path.exists(output_path) else 0
            }

            return report

        except Exception as e:
            logger.error(f"Erro ao criar relatório: {e}")
            return None

    @staticmethod
    def cleanup_temp_files(*file_paths):
        """Remove arquivos temporários"""
        cleaned = []
        errors = []

        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
                    cleaned.append(file_path)
                    logger.debug(f"Arquivo temporário removido: {file_path}")
            except Exception as e:
                errors.append(f"Erro ao remover {file_path}: {e}")
                logger.warning(f"Erro ao remover arquivo temporário {file_path}: {e}")

        return cleaned, errors


def _find_pyrevit_cli():
    """Encontra o executável do pyRevit CLI"""
    try:
        # Caminhos comuns do pyRevit
        possible_paths = [
            r"C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\pyrevit.exe",
            r"C:\Users\<USER>\AppData\Roaming\pyRevit\bin\pyrevit.exe",
            r"C:\pyRevit\bin\pyrevit.exe",
            r"C:\Program Files\pyRevit\bin\pyrevit.exe"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"✅ pyRevit CLI encontrado: {path}")
                return path

        # Tentar encontrar no PATH
        import shutil
        pyrevit_path = shutil.which('pyrevit')
        if pyrevit_path:
            logger.info(f"✅ pyRevit CLI encontrado no PATH: {pyrevit_path}")
            return pyrevit_path

        logger.warning("⚠️ pyRevit CLI não encontrado")
        return None

    except Exception as e:
        logger.error(f"❌ Erro ao procurar pyRevit CLI: {e}")
        return None


def convert_rfa_to_fbx(input_path, output_path=None, cleanup=True):
    """
    Função principal para converter RFA para FBX com máxima preservação de geometria

    Args:
        input_path (str): Caminho para o arquivo RFA
        output_path (str, optional): Caminho para o arquivo FBX de saída
        cleanup (bool): Se deve limpar arquivos temporários

    Returns:
        dict: Resultado da conversão com status e informações
    """
    try:
        # Validar arquivo de entrada
        is_valid, validation_msg = BimexConverter.validate_rfa_file(input_path)
        if not is_valid:
            return {
                'success': False,
                'error': f"Arquivo inválido: {validation_msg}",
                'input_path': input_path,
                'output_path': None
            }

        # Preparar caminho de saída
        if output_path is None:
            output_path = BimexConverter.prepare_output_path(input_path)

        if output_path is None:
            return {
                'success': False,
                'error': "Não foi possível preparar caminho de saída",
                'input_path': input_path,
                'output_path': None
            }

        # Executar conversão usando pyRevit CLI ou script alternativo
        import subprocess

        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Tentar usar pyRevit CLI primeiro para FBX
        pyrevit_script = os.path.join(script_dir, 'pyrevit_export_fbx_ironpython.py')
        pyrevit_exe = _find_pyrevit_cli()

        if pyrevit_exe and os.path.exists(pyrevit_script):
            # Usar pyRevit CLI para conversão FBX real
            # Sintaxe: pyrevit run script.py modelo.rfa --output modelo.fbx
            cmd = [pyrevit_exe, 'run', pyrevit_script, input_path, '--output', output_path, '--revit=2024', '--debug']
            logger.info(f"🚀 Usando pyRevit CLI para FBX (MÁXIMA QUALIDADE!): {' '.join(cmd)}")
            logger.info(f"✅ Conversão FBX com preservação completa de geometria!")
            logger.info(f"   - Materiais e texturas preservados")
            logger.info(f"   - Superfícies NURBS mantidas")
            logger.info(f"   - Detalhes geométricos completos")
            timeout = 900  # 15 minutos para pyRevit
        else:
            # Fallback para script alternativo FBX
            export_script = os.path.join(script_dir, 'export_fbx.py')
            if not os.path.exists(export_script):
                return {
                    'success': False,
                    'error': f"Scripts de exportação FBX não encontrados",
                    'input_path': input_path,
                    'output_path': output_path
                }

            cmd = [sys.executable, export_script, '--input', input_path, '--output', output_path]
            logger.info(f"🔄 Usando script FBX alternativo: {' '.join(cmd)}")
            timeout = 300  # 5 minutos para modo alternativo

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout
        )

        if result.returncode == 0:
            # Para pyRevit CLI, o arquivo FBX é criado no local especificado
            if pyrevit_exe and os.path.exists(pyrevit_script):
                # Arquivo FBX criado pelo pyRevit
                if os.path.exists(output_path):
                    return {
                        'success': True,
                        'input_path': input_path,
                        'output_path': output_path,
                        'output_size': os.path.getsize(output_path),
                        'format': 'FBX',
                        'quality': 'Maximum',
                        'features': [
                            'Materiais preservados',
                            'Texturas mantidas',
                            'Superfícies NURBS',
                            'Geometria completa'
                        ],
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }

            # Verificar se arquivo de saída foi criado (modo alternativo)
            if os.path.exists(output_path):
                return {
                    'success': True,
                    'input_path': input_path,
                    'output_path': output_path,
                    'output_size': os.path.getsize(output_path),
                    'format': 'FBX',
                    'quality': 'High',
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            else:
                return {
                    'success': False,
                    'error': "Arquivo FBX não foi criado",
                    'input_path': input_path,
                    'output_path': output_path,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
        else:
            return {
                'success': False,
                'error': f"Conversão falhou com código {result.returncode}",
                'input_path': input_path,
                'output_path': output_path,
                'stdout': result.stdout,
                'stderr': result.stderr
            }

    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': "Conversão excedeu tempo limite (5 minutos)",
            'input_path': input_path,
            'output_path': output_path
        }

    except Exception as e:
        logger.error(f"Erro durante conversão: {e}")
        return {
            'success': False,
            'error': f"Erro interno: {str(e)}",
            'input_path': input_path,
            'output_path': output_path
        }


if __name__ == "__main__":
    # Teste básico se executado diretamente
    import argparse
    from datetime import datetime

    parser = argparse.ArgumentParser(description="Utilitário de conversão BIMEX")
    parser.add_argument('--input', required=True, help="Arquivo RFA de entrada")
    parser.add_argument('--output', help="Arquivo FBX de saída (opcional)")
    parser.add_argument('--info', action='store_true', help="Mostrar apenas informações do arquivo")

    args = parser.parse_args()

    if args.info:
        # Mostrar informações do arquivo
        info = BimexConverter.get_family_info(args.input)
        if info:
            print(json.dumps(info, indent=2))
        else:
            print("Erro ao obter informações do arquivo")
            sys.exit(1)
    else:
        # Executar conversão para FBX
        result = convert_rfa_to_fbx(args.input, args.output)
        print(json.dumps(result, indent=2))

        if not result['success']:
            sys.exit(1)
