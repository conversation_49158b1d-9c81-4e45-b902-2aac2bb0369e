# Conversor RFA → OBJ via Modelo de Projeto

## Visão Geral

Este documento explica a nova abordagem de conversão de famílias Revit (.rfa) para arquivos 3D (.obj) através da criação de um modelo de projeto.

## Diferenças entre as Abordagens

### Abordagem Original (`working_rfa_to_obj.py`)
- **Método**: Abre diretamente o arquivo de família (.rfa)
- **Processamento**: Extrai geometria diretamente da família
- **Limitações**: Algumas famílias podem ter geometria que só é totalmente resolvida quando inserida em um projeto

### Nova Abordagem (`rfa_to_obj_via_project.py`)
- **Método**: Cria um modelo de projeto, carrega e insere a família na origem
- **Processamento**: Extrai geometria da instância da família no projeto
- **Vantagens**: 
  - Geometria totalmente resolvida e renderizada
  - Comportamento mais próximo ao uso real no Revit
  - Melhor handling de famílias complexas com parâmetros

## Fluxo da Nova Abordagem

```
1. 📂 Abrir arquivo .rfa para verificação
2. 🏗️ Criar novo documento de projeto Revit
3. 📥 Carregar família no projeto
4. 📍 Inserir instância da família na origem (0,0,0)
5. 🔄 Processar geometria da instância no projeto
6. 📝 Gerar arquivo .obj
7. 🧹 Limpar recursos (fechar documentos)
```

## Arquivos

### `rfa_to_obj_via_project.py`
Script principal com a nova implementação.

**Uso:**
```bash
pyrevit run rfa_to_obj_via_project.py arquivo.rfa [arquivo.obj]
```

### `test_project_converter.py`
Script de teste que valida o funcionamento do conversor.

**Uso:**
```bash
python test_project_converter.py
```

## Funções Principais

### `create_new_project_document(app)`
- Cria um novo documento de projeto Revit
- Tenta primeiro com template métrico, depois imperial
- Retorna o documento criado ou None se falhar

### `load_family_into_project(doc, family_file_path)`
- Carrega uma família no documento de projeto
- Usa Transaction para garantir consistência
- Retorna a família carregada ou None se falhar

### `insert_family_at_origin(doc, family)`
- Insere uma instância da família na origem (0,0,0)
- Ativa o símbolo da família se necessário
- Retorna a instância criada ou None se falhar

### `process_project_geometry_to_obj(doc, output_file, family_title)`
- Coleta instâncias de família do projeto
- Filtra elementos com geometria sólida válida
- Processa geometria e cria arquivo OBJ

## Vantagens da Nova Abordagem

1. **Geometria Completa**: A família é totalmente resolvida quando inserida no projeto
2. **Parâmetros Aplicados**: Valores padrão de parâmetros são aplicados corretamente
3. **Comportamento Real**: Simula o uso real da família no Revit
4. **Melhor Compatibilidade**: Funciona melhor com famílias complexas
5. **Debugging**: Mais fácil de debugar problemas de geometria

## Considerações

### Performance
- Ligeiramente mais lento que a abordagem direta
- Requer criação e limpeza de documento temporário

### Recursos
- Usa mais memória temporariamente
- Requer limpeza cuidadosa de recursos

### Compatibilidade
- Funciona com as mesmas versões do Revit
- Requer pyRevit instalado e configurado

## Teste

Para testar o conversor:

1. Certifique-se de que o arquivo de teste existe:
   ```
   C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa
   ```

2. Execute o teste:
   ```bash
   python pyrevit-scripts/test_project_converter.py
   ```

3. Verifique se o arquivo .obj foi criado com sucesso

## Integração

Para integrar com o sistema BIMEX, substitua a chamada do conversor original pela nova versão:

```python
# Antes
result = subprocess.run([
    pyrevit_exe, 'run', 'working_rfa_to_obj.py', input_file
])

# Depois  
result = subprocess.run([
    pyrevit_exe, 'run', 'rfa_to_obj_via_project.py', input_file
])
```

## Troubleshooting

### Erro ao criar documento de projeto
- Verifique se o Revit está instalado corretamente
- Certifique-se de que há templates disponíveis

### Erro ao carregar família
- Verifique se o arquivo .rfa é válido
- Confirme se a família não está corrompida

### Erro ao inserir instância
- Verifique se a família tem símbolos válidos
- Confirme se não há conflitos de parâmetros

### Arquivo OBJ vazio
- Verifique se a família tem geometria sólida
- Confirme se os elementos não estão ocultos
