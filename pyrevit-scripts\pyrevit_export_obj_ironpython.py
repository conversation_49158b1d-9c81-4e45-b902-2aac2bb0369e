#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit para conversão de arquivos RFA para OBJ
Compatível com IronPython 2 (usado pelo pyRevit)

Uso via pyRevit CLI:
    pyrevit run pyrevit_export_obj_ironpython.py arquivo.rfa --output arquivo.obj
"""

import sys
import os
import tempfile

# Importar APIs do Revit (disponíveis quando executado via pyRevit)
import clr
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('RevitServices')

from Autodesk.Revit.DB import *
from Autodesk.Revit.ApplicationServices import Application
from RevitServices.Persistence import DocumentManager
from RevitServices.Transactions import TransactionManager

# Importar utilitários do pyRevit
from pyrevit import revit, DB, UI, script

print("=== BIMEX pyRevit OBJ Exporter ===")
print("APIs do Revit carregadas com sucesso")

class PyRevitOBJExporter:
    """Exportador OBJ usando pyRevit e APIs do Revit"""

    def __init__(self):
        self.app = None
        self.doc = None

    def initialize(self):
        """Inicializa conexão com Revit"""
        try:
            # Usar pyRevit context
            self.app = revit.app
            print("Aplicacao pyRevit inicializada")
            return True

        except Exception as e:
            print("Erro ao inicializar: " + str(e))
            return False

    def open_family(self, rfa_path):
        """Abre arquivo de família RFA"""
        try:
            if not os.path.exists(rfa_path):
                raise Exception("Arquivo nao encontrado: " + rfa_path)

            print("Abrindo familia: " + rfa_path)

            # Abrir documento de família
            self.doc = self.app.OpenDocumentFile(rfa_path)

            if self.doc is None:
                raise Exception("Falha ao abrir documento")

            print("Familia aberta: " + self.doc.Title)
            return True

        except Exception as e:
            print("Erro ao abrir familia: " + str(e))
            return False

    def export_to_obj(self, output_path):
        """Exporta família para OBJ"""
        try:
            if self.doc is None:
                raise Exception("Nenhum documento carregado")

            print("Exportando para OBJ: " + output_path)

            # Criar diretório de saída
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Coletar elementos com geometria
            collector = FilteredElementCollector(self.doc)
            elements = collector.WhereElementIsNotElementType().ToElements()

            # Filtrar elementos com geometria 3D
            geometric_elements = []
            for element in elements:
                try:
                    geom = element.get_Geometry(Options())
                    if geom is not None:
                        geometric_elements.append(element)
                except:
                    continue

            print("Encontrados " + str(len(geometric_elements)) + " elementos com geometria")

            if not geometric_elements:
                print("Nenhum elemento geometrico encontrado")
                return self._create_empty_obj(output_path)

            # Extrair geometria e criar OBJ
            return self._extract_geometry_to_obj(geometric_elements, output_path)

        except Exception as e:
            print("Erro durante exportacao: " + str(e))
            return False

    def _extract_geometry_to_obj(self, elements, output_path):
        """Extrai geometria dos elementos e cria arquivo OBJ"""
        try:
            vertices = []
            faces = []
            vertex_count = 0

            print("Extraindo geometria dos elementos...")

            # Configurar opções de geometria para máxima qualidade
            geom_options = Options()
            geom_options.DetailLevel = ViewDetailLevel.Fine  # Máximo nível de detalhe
            geom_options.ComputeReferences = True  # Computar referências para geometria detalhada
            geom_options.IncludeNonVisibleObjects = True  # Incluir objetos não visíveis

            print("Configuracoes de alta qualidade aplicadas:")
            print("   - DetailLevel: Fine (maximo detalhe)")
            print("   - ComputeReferences: True")
            print("   - IncludeNonVisibleObjects: True")

            for element in elements:
                try:
                    geom = element.get_Geometry(geom_options)
                    if geom is None:
                        continue

                    for geom_obj in geom:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            # Processar sólido com máxima qualidade
                            solid_vertices, solid_faces = self._process_solid_high_quality(geom_obj, vertex_count)
                            vertices.extend(solid_vertices)
                            faces.extend(solid_faces)
                            vertex_count += len(solid_vertices)

                        elif hasattr(geom_obj, 'GetInstanceGeometry'):
                            # Processar instância de geometria com máxima qualidade
                            try:
                                inst_geom = geom_obj.GetInstanceGeometry()
                                for inst_obj in inst_geom:
                                    if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                        solid_vertices, solid_faces = self._process_solid_high_quality(inst_obj, vertex_count)
                                        vertices.extend(solid_vertices)
                                        faces.extend(solid_faces)
                                        vertex_count += len(solid_vertices)
                            except:
                                continue

                        elif isinstance(geom_obj, Mesh):
                            # Processar meshes diretamente para preservar detalhes
                            mesh_vertices, mesh_faces = self._process_mesh_high_quality(geom_obj, vertex_count)
                            vertices.extend(mesh_vertices)
                            faces.extend(mesh_faces)
                            vertex_count += len(mesh_vertices)

                except Exception as e:
                    print("Erro ao processar elemento " + str(element.Id) + ": " + str(e))
                    continue

            # Escrever arquivo OBJ
            with open(output_path, 'w') as f:
                f.write("# Arquivo OBJ exportado pelo BIMEX pyRevit Converter\n")
                f.write("# Familia: " + self.doc.Title + "\n")
                f.write("# Elementos processados: " + str(len(elements)) + "\n")
                f.write("# Vertices: " + str(len(vertices)) + "\n")
                f.write("# Faces: " + str(len(faces)) + "\n\n")

                # Escrever vértices
                for vertex in vertices:
                    f.write(vertex + "\n")

                f.write("\n")

                # Escrever faces
                for face in faces:
                    f.write(face + "\n")

            print("OBJ criado: " + str(len(vertices)) + " vertices, " + str(len(faces)) + " faces")
            return True

        except Exception as e:
            print("Erro ao extrair geometria: " + str(e))
            return False

    def _process_solid(self, solid, vertex_offset):
        """Processa um sólido e retorna vértices e faces"""
        vertices = []
        faces = []

        try:
            # Triangular todas as faces do sólido
            for face in solid.Faces:
                try:
                    mesh = face.Triangulate()
                    if mesh is not None:
                        # Adicionar vértices do mesh
                        for i in range(mesh.NumTriangles):
                            triangle = mesh.get_Triangle(i)
                            for j in range(3):
                                vertex = triangle.get_Vertex(j)
                                # Converter unidades do Revit (pés) para metros
                                x = vertex.X * 0.3048
                                y = vertex.Y * 0.3048
                                z = vertex.Z * 0.3048
                                vertices.append("v {0:.6f} {1:.6f} {2:.6f}".format(x, y, z))

                        # Adicionar faces (triângulos)
                        for i in range(mesh.NumTriangles):
                            v1 = vertex_offset + (i * 3) + 1
                            v2 = vertex_offset + (i * 3) + 2
                            v3 = vertex_offset + (i * 3) + 3
                            faces.append("f {0} {1} {2}".format(v1, v2, v3))
                except:
                    continue

        except Exception as e:
            print("Erro ao processar solido: " + str(e))

        return vertices, faces

    def _process_solid_high_quality(self, solid, vertex_offset):
        """Processa um sólido com máxima qualidade e preservação de detalhes"""
        vertices = []
        faces = []

        try:
            print("Processando solido com " + str(solid.Faces.Size) + " faces")

            # Triangular todas as faces do sólido com máxima qualidade
            for face in solid.Faces:
                try:
                    # Usar triangulação padrão (sem parâmetros de simplificação)
                    mesh = face.Triangulate()
                    if mesh is not None:
                        print("   Face triangulada: " + str(mesh.NumTriangles) + " triangulos")

                        # Adicionar vértices do mesh com máxima precisão
                        for i in range(mesh.NumTriangles):
                            triangle = mesh.get_Triangle(i)
                            for j in range(3):
                                vertex = triangle.get_Vertex(j)
                                # Converter unidades do Revit (pés) para metros com alta precisão
                                x = vertex.X * 0.3048
                                y = vertex.Y * 0.3048
                                z = vertex.Z * 0.3048
                                # Usar 8 casas decimais para máxima precisão
                                vertices.append("v {0:.8f} {1:.8f} {2:.8f}".format(x, y, z))

                        # Adicionar faces (triângulos) preservando ordem correta
                        for i in range(mesh.NumTriangles):
                            v1 = vertex_offset + (i * 3) + 1
                            v2 = vertex_offset + (i * 3) + 2
                            v3 = vertex_offset + (i * 3) + 3
                            faces.append("f {0} {1} {2}".format(v1, v2, v3))
                    else:
                        print("   Face nao pode ser triangulada ou esta vazia")

                except:
                    print("   Erro ao triangular face")
                    continue

        except Exception as e:
            print("Erro ao processar solido com alta qualidade: " + str(e))

        print("Solido processado: " + str(len(vertices)) + " vertices, " + str(len(faces)) + " faces")
        return vertices, faces

    def _process_mesh_high_quality(self, mesh, vertex_offset):
        """Processa um mesh diretamente com máxima qualidade"""
        vertices = []
        faces = []

        try:
            print("Processando mesh direto com " + str(mesh.NumTriangles) + " triangulos")

            # Processar triângulos do mesh diretamente
            for i in range(mesh.NumTriangles):
                triangle = mesh.get_Triangle(i)

                # Adicionar vértices com máxima precisão
                for j in range(3):
                    vertex = triangle.get_Vertex(j)
                    # Converter unidades do Revit (pés) para metros com alta precisão
                    x = vertex.X * 0.3048
                    y = vertex.Y * 0.3048
                    z = vertex.Z * 0.3048
                    # Usar 8 casas decimais para máxima precisão
                    vertices.append("v {0:.8f} {1:.8f} {2:.8f}".format(x, y, z))

                # Adicionar face
                v1 = vertex_offset + (i * 3) + 1
                v2 = vertex_offset + (i * 3) + 2
                v3 = vertex_offset + (i * 3) + 3
                faces.append("f {0} {1} {2}".format(v1, v2, v3))

        except Exception as e:
            print("Erro ao processar mesh direto: " + str(e))

        print("Mesh processado: " + str(len(vertices)) + " vertices, " + str(len(faces)) + " faces")
        return vertices, faces

    def _create_empty_obj(self, output_path):
        """Cria arquivo OBJ vazio para famílias sem geometria"""
        try:
            with open(output_path, 'w') as f:
                f.write("# Arquivo OBJ - Familia sem geometria 3D\n")
                f.write("# Familia: " + self.doc.Title + "\n")
                f.write("# Nenhum elemento geometrico encontrado\n")

            print("Arquivo OBJ vazio criado")
            return True

        except Exception as e:
            print("Erro ao criar OBJ vazio: " + str(e))
            return False

    def close_document(self):
        """Fecha documento sem salvar"""
        try:
            if self.doc is not None:
                self.doc.Close(False)  # False = não salvar
                print("Documento fechado")
        except Exception as e:
            print("Erro ao fechar documento: " + str(e))


def main():
    """Função principal"""
    try:
        print("=== Iniciando conversao RFA para OBJ ===")

        # Obter argumentos da linha de comando
        args = sys.argv
        print("Argumentos recebidos: " + str(args))

        if len(args) < 2:
            print("Uso: pyrevit run script.py arquivo.rfa --output arquivo.obj")
            return 1

        input_file = args[1]  # Primeiro argumento é o arquivo RFA

        # Procurar argumento --output
        output_file = None
        for i, arg in enumerate(args):
            if arg == "--output" and i + 1 < len(args):
                output_file = args[i + 1]
                break

        if output_file is None:
            # Gerar nome do arquivo OBJ baseado no RFA
            base_name = os.path.splitext(input_file)[0]
            output_file = base_name + ".obj"

        print("Arquivo entrada: " + input_file)
        print("Arquivo saida: " + output_file)

        # Verificar se arquivo de entrada existe
        if not os.path.exists(input_file):
            print("ERRO: Arquivo de entrada nao encontrado: " + input_file)
            return 1

        # Executar conversão
        exporter = PyRevitOBJExporter()

        # Inicializar
        if not exporter.initialize():
            return 1

        # Abrir família
        if not exporter.open_family(input_file):
            return 1

        # Exportar para OBJ
        if not exporter.export_to_obj(output_file):
            return 1

        print("=== Conversao concluida com sucesso! ===")
        return 0

    except Exception as e:
        print("Erro durante conversao: " + str(e))
        return 1

    finally:
        # Limpar recursos
        try:
            exporter.close_document()
        except:
            pass


# Executar função principal
if __name__ == "__main__":
    result = main()
    sys.exit(result)
